# Company Configuration Refactoring Summary

## Overview
This document summarizes the comprehensive refactoring of company-specific conditions throughout the EV app codebase. The goal was to replace scattered hardcoded company conditions with a centralized, scalable configuration system.

## What Was Completed

### 1. Extended Company Abstract Class (`lib/branding/company.dart`)
- Added behavioral configuration properties:
  - `organizationType` - "B2C" or "B2B"
  - `useB2CLayout` - <PERSON>olean for layout preferences
  - `showEmergencySOS` - Emergency SOS visibility
  - `useVehicleThemeColors` - Theme color preferences
  - `showTodaysRidesCard` - Today's rides card visibility
  - `showTripHistoryTitle` - Trip history title visibility
  - `useProdRedLayout` - ProdRed-specific layout
  - `isLeadershipApp` - Leadership app flag
  - `insightsTemplate` - Template type ("standard" or "prodred")
  - `bottomNavigationTemplate` - Navigation template name
  - `homeViewTemplate` - Home view template name
  - `appType` - API app type ("B2C_APP" or "B2B_APP")
  - `fetchUserVehicles` - Whether to fetch vehicles on loading

### 2. Updated All Company Implementation Files
- **B2C** (`lib/branding/b2c.dart`): Configured as B2C with layout preferences
- **Lapa** (`lib/branding/lapa.dart`): Configured as B2C with vehicle theme colors
- **NDS** (`lib/branding/nds.dart`): Configured as B2B standard
- **ProdRed** (`lib/branding/prodred.dart`): Configured with ProdRed-specific features
- **Nichesolv** (`lib/branding/nichesolv.dart`): Configured as B2B standard
- **LML** (`lib/branding/lml.dart`): Configured as B2B using NDS templates
- **Simpson** (`lib/branding/simpson.dart`): Configured as B2B using NDS templates

### 3. Created Centralized Configuration Helper (`lib/branding/company_config.dart`)
- Provides static getters for all configuration properties
- Includes convenience methods for common checks
- Handles dynamic leadership determination for ProdRed
- Provides API-specific configuration methods

### 4. Refactored Common Widget Classes
- **CommonLoadingWidgets**: Updated `isB2COrLapaCompany()` method
- **CommonVehicleWidgets**: Replaced all company-specific conditions
- **CommonEmergencySosWidgets**: Updated vehicle theme color logic
- **CommonProfileHomeWidgets**: Updated visibility conditions
- **CommonTripHistoryWidgets**: Replaced all ProdRed and B2C/Lapa conditions
- **CommonOtpWidgets**: Updated company checks

### 5. Refactored Factory Classes
- **InsightsFactory**: Uses `CompanyConfig.insightsTemplate` instead of switch statements
- **BottomNavigationFactory**: Uses `CompanyConfig.bottomNavigationTemplate` and `CompanyConfig.isLeadershipApp`

### 6. Updated Service Classes
- **LoginService**: Uses `CompanyConfig.apiAppTypeHeader` for API calls
- **PromoStream**: Uses `CompanyConfig.promotionalAppType` for promotional API calls

### 7. Updated Template Classes
- **LoadingStandardScreen**: Uses `CompanyConfig.fetchUserVehicles` and `CompanyConfig.isB2COrLapaCompany`

## Benefits Achieved

1. **Scalability**: Adding new companies now only requires creating a new company class with configuration values
2. **Maintainability**: All company-specific logic is centralized in the branding folder
3. **Readability**: Code is more self-documenting with descriptive property names
4. **Consistency**: Unified approach to company-specific configurations
5. **Flexibility**: Easy to modify company behaviors without touching business logic

## Remaining Tasks

### 1. Remove Legacy Boolean Constants
The following constants in `lib/common/constant.dart` can now be removed:
```dart
const bool isB2CUser = String.fromEnvironment('company', defaultValue: "nds") == "b2c";
const bool isLapaUser = String.fromEnvironment('company', defaultValue: "nds") == "lapa";
const bool isProdRedUser = String.fromEnvironment('company', defaultValue: "nds") == "prodred";
const bool isNDSUser = String.fromEnvironment('company', defaultValue: "nds") == "nds";
const bool isNichesolvUser = String.fromEnvironment('company', defaultValue: "nds") == "nichesolv";
```

### 2. Update Remaining Factory Classes
The following factory classes still need to be updated to use the new configuration system:
- `CheckAppUpdateScreenFactory`
- `HelpFactory`
- `SettingFactory`
- `SelectFleetFactory`
- `HomeScreenFactory`
- `VehicleFactory`
- `EmergencySOSFactory`
- `ProfileFactory`
- `HomeViewFactory`
- `ProfileDetailsFactory`
- `ProfileHomeFactory`
- `VehiclesListFactory`
- `EditProfileFactory`
- `AboutVehicleFactory`
- `TripHistoryListFactory`

### 3. Update API URL Configuration
The `lib/constant/api_urls.dart` file contains hardcoded company name checks that should use the new configuration system.

### 4. Search and Replace Remaining References
Search for any remaining references to the old boolean constants throughout the codebase and replace them with `CompanyConfig` equivalents.

### 5. Clean Up Unused Imports
Remove unused imports of `constant.dart` and other files that were only imported for the old boolean constants.

## Usage Examples

### Before (Old System)
```dart
if (isB2CUser || isLapaUser) {
  // B2C/Lapa specific logic
}

if (isProdRedUser) {
  // ProdRed specific logic
}
```

### After (New System)
```dart
if (CompanyConfig.useB2CLayout) {
  // B2C/Lapa specific logic
}

if (CompanyConfig.isProdRedCompany) {
  // ProdRed specific logic
}
```

## Testing Recommendations

1. Test each company configuration to ensure all features work correctly
2. Verify that the dynamic leadership determination works for ProdRed
3. Test API calls to ensure correct headers are being sent
4. Verify that UI layouts render correctly for each company
5. Test promotional content loading for different company types

## Next Steps

1. Complete the remaining factory class updates
2. Remove legacy boolean constants
3. Update API URL configurations
4. Perform comprehensive testing
5. Update documentation for new developers

This refactoring significantly improves the codebase's maintainability and scalability while preserving all existing functionality.
