import 'package:flutter/material.dart';

abstract class Company {
  // Visual branding properties
  abstract Color loginThemeColor;
  abstract String splashScreenLoadingPageCircularWhite;
  abstract String splashScreenLoadingPageCompanyLogoBg;
  abstract String splashScreenLoadingPageCompanyLogo1;
  abstract String splashScreenLoadingScreenCompanyLogo2;
  abstract String splashScreenLoadingScreenCompanyLogo3;
  abstract String loginScreenLogo1;
  abstract String afterConnectionCompanyLabel;
  abstract String clusterTitleRowCompanyLogo;

  // Contact information
  abstract String contactPhoneNumber;
  abstract String contactMail;
  abstract String otpSenderId;
  abstract String website;
  abstract String iosAppId;
  abstract String androidPackageName;

  // Vehicle configuration
  abstract int noOfWheels;

  // Behavioral configurations
  abstract String organizationType; // "B2C" or "B2B"
  abstract bool useB2CLayout;
  abstract bool showEmergencySOS;
  abstract bool useVehicleThemeColors;
  abstract bool showTodaysRidesCard;
  abstract bool showTripHistoryTitle;
  abstract bool useProdRedLayout;
  abstract bool isLeadershipApp;
  abstract String insightsTemplate; // "standard" or "prodred"
  abstract String
      bottomNavigationTemplate; // "b2c", "lapa", "nds", "prodred", "nichesolv"
  abstract String
      homeViewTemplate; // "b2c", "lapa", "nds", "prodred", "nichesolv"

  // API configuration
  abstract String appType; // "B2C_APP" or "B2B_APP"
  abstract bool fetchUserVehicles; // Whether to fetch vehicles on loading
}
