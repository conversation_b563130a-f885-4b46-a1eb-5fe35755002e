import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/constant.dart';

/// Centralized company configuration helper
/// Replaces all scattered company-specific boolean checks and conditions
/// with a single, scalable configuration system
class CompanyConfig {
  
  /// Gets the organization type for API calls
  /// Returns "B2C" for B2C/Lapa companies, "B2B" for others
  static String get organizationType => company.organizationType;
  
  /// Determines if B2C layout should be used
  /// Returns true for B2C and Lapa companies
  static bool get useB2CLayout => company.useB2CLayout;
  
  /// Determines if Emergency SOS should be shown
  static bool get showEmergencySOS => company.showEmergencySOS;
  
  /// Determines if vehicle theme colors should be used
  /// B2C users get colorGrey800, others get vehicle theme colors when available
  static bool get useVehicleThemeColors => company.useVehicleThemeColors;
  
  /// Determines if today's rides card should be shown
  /// Only for ProdRed users
  static bool get showTodaysRidesCard => company.showTodaysRidesCard;
  
  /// Determines if trip history title should be shown
  /// Only for B2C and Lapa users when there's trip history
  static bool get showTripHistoryTitle => company.showTripHistoryTitle;
  
  /// Determines if ProdRed-specific UI layout should be used
  static bool get useProdRedLayout => company.useProdRedLayout;
  
  /// Determines if this is a leadership app
  /// For ProdRed, this is determined dynamically from SharedPreferences
  static bool get isLeadershipApp {
    if (companyName == 'prodred') {
      return isProdRedLeadership;
    }
    return company.isLeadershipApp;
  }
  
  /// Gets the insights template type
  /// Returns "prodred" for ProdRed, "standard" for others
  static String get insightsTemplate => company.insightsTemplate;
  
  /// Gets the bottom navigation template
  static String get bottomNavigationTemplate => company.bottomNavigationTemplate;
  
  /// Gets the home view template
  static String get homeViewTemplate => company.homeViewTemplate;
  
  /// Gets the app type for API calls
  /// Returns "B2C_APP" or "B2B_APP"
  static String get appType => company.appType;
  
  /// Determines if user vehicles should be fetched on loading
  /// Only B2C/Lapa apps should fetch riders/vehicles via B2C endpoint
  static bool get fetchUserVehicles => company.fetchUserVehicles;
  
  // Convenience methods for common checks
  
  /// Checks if the current company is B2C or Lapa
  static bool get isB2COrLapaCompany => useB2CLayout;
  
  /// Checks if the current company is ProdRed
  static bool get isProdRedCompany => useProdRedLayout;
  
  /// Checks if the current company is B2C
  static bool get isB2CCompany => companyName == 'b2c';
  
  /// Checks if the current company is Lapa
  static bool get isLapaCompany => companyName == 'lapa';
  
  /// Checks if the current company is NDS
  static bool get isNDSCompany => companyName == 'nds';
  
  /// Checks if the current company is Nichesolv
  static bool get isNichesolvCompany => companyName == 'nichesolv';
  
  /// Gets the full organization type name for API calls
  static String get organizationTypeFullName {
    return organizationType == "B2C" ? "B2C" : "B2B";
  }
  
  /// Gets the appropriate app type for promotional API calls
  static String get promotionalAppType {
    return useB2CLayout ? "B2C_APP" : "B2B_APP";
  }
  
  /// Gets the appropriate app type header for API calls
  static String get apiAppTypeHeader {
    return useB2CLayout ? "B2C" : "B2B";
  }
}
