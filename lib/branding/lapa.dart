import 'package:flutter/material.dart';
import 'package:nds_app/branding/company.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';

class Lapa extends Company {
  @override
  String splashScreenLoadingPageCircularWhite =
      splashScreenImages["circularWhiteAnimation"]!;

  @override
  String splashScreenLoadingPageCompanyLogoBg =
      splashScreenImages["lapa_Logo_Bg"]!;

  @override
  String splashScreenLoadingPageCompanyLogo1 =
      splashScreenImages["lapa_logo_1"]!;

  @override
  String splashScreenLoadingScreenCompanyLogo2 =
      splashScreenImages["lapa_logo_1"]!;

  @override
  String splashScreenLoadingScreenCompanyLogo3 =
      splashScreenImages["lapa_logo_2"]!;

  @override
  String loginScreenLogo1 = loginScreenImages["loginScreenImageLapa"]!;

  @override
  String afterConnectionCompanyLabel = homeScreenText['text19']!;

  @override
  String clusterTitleRowCompanyLogo = splashScreenImages["lapa_logo_1"]!;

  @override
  Color loginThemeColor = colorLapaTheme;

  @override
  String contactMail = lapaContactMail;

  @override
  String contactPhoneNumber = lapaContactPhoneNumber;

  @override
  String otpSenderId = nichesolvOtpSenderId;

  @override
  String website = lapaWebsite;

  @override
  String iosAppId = lapaB2CIosAppId;

  @override
  String androidPackageName = lapaB2CAndroidPackageName;

  @override
  int noOfWheels = lapaWheels;

  // Behavioral configurations
  @override
  String organizationType = "B2C";

  @override
  bool useB2CLayout = true;

  @override
  bool showEmergencySOS = true;

  @override
  bool useVehicleThemeColors = true; // Lapa users get vehicle theme colors

  @override
  bool showTodaysRidesCard = false;

  @override
  bool showTripHistoryTitle = true;

  @override
  bool useProdRedLayout = false;

  @override
  bool isLeadershipApp = false;

  @override
  String insightsTemplate = "standard";

  @override
  String bottomNavigationTemplate = "lapa";

  @override
  String homeViewTemplate = "lapa";

  // API configuration
  @override
  String appType = "B2C_APP";

  @override
  bool fetchUserVehicles = true;
}
