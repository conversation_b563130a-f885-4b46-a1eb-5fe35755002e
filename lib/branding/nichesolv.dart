import 'package:flutter/material.dart';
import 'package:nds_app/branding/company.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/strings.dart';

class Nichesolv extends Company {
  @override
  String splashScreenLoadingPageCircularWhite =
      splashScreenImages["circularWhiteAnimation"]!;

  @override
  String splashScreenLoadingPageCompanyLogoBg =
      splashScreenImages["nichesolv_Logo_Bg"]!;

  @override
  String splashScreenLoadingPageCompanyLogo1 =
      splashScreenImages["nichesolv_logo_1"]!;

  @override
  String splashScreenLoadingScreenCompanyLogo2 =
      splashScreenImages["nichesolv_logo_1"]!;

  @override
  String splashScreenLoadingScreenCompanyLogo3 =
      splashScreenImages["nichesolv_logo_2"]!;

  @override
  String loginScreenLogo1 = loginScreenImages["loginScreenImageGreen"]!;

  @override
  String afterConnectionCompanyLabel = homeScreenText['text16']!;

  @override
  String clusterTitleRowCompanyLogo = splashScreenImages["nichesolv_logo_1"]!;

  @override
  Color loginThemeColor = colorGreenNichesolvTheme;

  @override
  String contactMail = nichesolvContactMail;

  @override
  String contactPhoneNumber = nichesolvContactPhoneNumber;

  @override
  String otpSenderId = nichesolvOtpSenderId;

  @override
  String website = nichesolvWebsite;

  @override
  String iosAppId = nichesolvB2BIosAppId;

  @override
  String androidPackageName = nichesolvB2BAndroidPackageName;

  @override
  int noOfWheels = nichesolvWheels;

  // Behavioral configurations
  @override
  String organizationType = "B2B";

  @override
  bool useB2CLayout = false;

  @override
  bool showEmergencySOS = true;

  @override
  bool useVehicleThemeColors = true;

  @override
  bool showTodaysRidesCard = false;

  @override
  bool showTripHistoryTitle = false;

  @override
  bool useProdRedLayout = false;

  @override
  bool isLeadershipApp = false;

  @override
  String insightsTemplate = "standard";

  @override
  String bottomNavigationTemplate = "nichesolv";

  @override
  String homeViewTemplate = "nichesolv";

  // API configuration
  @override
  String appType = "B2B_APP";

  @override
  bool fetchUserVehicles = false;
}
