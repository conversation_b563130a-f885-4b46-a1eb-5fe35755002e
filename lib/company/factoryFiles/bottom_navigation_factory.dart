import 'package:flutter/material.dart';
import 'package:nds_app/models/enums/color_type.dart';
import '../../branding/branding.dart';
import '../../branding/company_config.dart';
import '../../common/constant.dart';
import '../widgets/dashboard/bottom_navigation_b2c.dart';
import '../widgets/dashboard/bottom_navigation_lapa.dart';
import '../widgets/dashboard/bottom_navigation_nds.dart';
import '../widgets/dashboard/bottom_navigation_prodred.dart';
import '../widgets/dashboard/bottom_navigation_prodred_leadership.dart';
import '../widgets/dashboard/bottom_navigation_nichesolv.dart';

/// Factory class for bottom navigation - uses company-specific implementations
class BottomNavigationFactory {
  /// Creates and returns the getBody widget based on company
  static Widget createGetBody(
      Function action, BuildContext context, Color color, ColorType colorType) {
    switch (CompanyConfig.bottomNavigationTemplate) {
      case 'b2c':
        return BottomNavigationB2C.getBody(action, context, color, colorType);
      case 'lapa':
        return BottomNavigationLapa.getBody(action, context, color, colorType);
      case 'nds':
        return BottomNavigationNDS.getBody(action, context, color, colorType);
      case 'prodred':
        return CompanyConfig.isLeadershipApp
            ? BottomNavigationProdRedLeadership.getBody(
                action, context, color, colorType)
            : BottomNavigationProdRed.getBody(
                action, context, color, colorType);
      case 'nichesolv':
        return BottomNavigationNichesolv.getBody(
            action, context, color, colorType);
      default:
        return BottomNavigationNDS.getBody(action, context, color, colorType);
    }
  }

  /// Creates and returns the getCustomBottomNavigationBar widget based on company
  static Widget createGetCustomBottomNavigationBar(
      BuildContext context, Function action, Color color, ColorType colorType) {
    switch (CompanyConfig.bottomNavigationTemplate) {
      case 'b2c':
        return BottomNavigationB2C.getCustomBottomNavigationBar(
            context, action, color, colorType);
      case 'lapa':
        return BottomNavigationLapa.getCustomBottomNavigationBar(
            context, action, color, colorType);
      case 'nds':
        return BottomNavigationNDS.getCustomBottomNavigationBar(
            context, action, color, colorType);
      case 'prodred':
        return CompanyConfig.isLeadershipApp
            ? BottomNavigationProdRedLeadership.getCustomBottomNavigationBar(
                context, action, color, colorType)
            : BottomNavigationProdRed.getCustomBottomNavigationBar(
                context, action, color, colorType);
      case 'nichesolv':
        return BottomNavigationNichesolv.getCustomBottomNavigationBar(
            context, action, color, colorType);
      default:
        return BottomNavigationNDS.getCustomBottomNavigationBar(
            context, action, color, colorType);
    }
  }

  /// Returns the company-specific bottom navigation class name for debugging purposes
  static String getBottomNavigationClassName() {
    switch (CompanyConfig.bottomNavigationTemplate) {
      case 'b2c':
        return 'BottomNavigationB2C';
      case 'lapa':
        return 'BottomNavigationLapa';
      case 'nds':
        return 'BottomNavigationNDS';
      case 'prodred':
        return CompanyConfig.isLeadershipApp
            ? 'BottomNavigationProdRedLeadership'
            : 'BottomNavigationProdRed';
      case 'nichesolv':
        return 'BottomNavigationNichesolv';
      default:
        return 'BottomNavigationNDS';
    }
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
