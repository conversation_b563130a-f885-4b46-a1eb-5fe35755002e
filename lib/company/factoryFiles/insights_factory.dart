import 'package:flutter/material.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/branding/company_config.dart';
import 'package:nds_app/company/baseScreens/insights/base_insights.dart';
import 'package:nds_app/company/templates/insights/insights_prodred_screen.dart';
import 'package:nds_app/company/templates/insights/insights_standard_screen.dart';

/// Factory class to create company-specific insights screens based on the current company configuration
class InsightsFactory {
  /// Creates and returns the appropriate insights widget based on the company name
  static Widget createInsights(Color color, ColorType colorType) {
    // Use company-specific validation to determine template
    return _getInsightsTemplate(color: color, colorType: colorType);
  }

  /// Returns the appropriate insights template based on company validation requirements
  static BaseInsights _getInsightsTemplate({
    required Color color,
    required ColorType colorType,
  }) {
    // Use company configuration to determine insights template
    switch (CompanyConfig.insightsTemplate) {
      case 'prodred':
        return InsightsProdRedScreen(color: color, colorType: colorType);
      case 'standard':
      default:
        return InsightsStandardScreen(color: color, colorType: colorType);
    }
  }

  /// Returns the company-specific insights template class name for debugging purposes
  static String getInsightsClassName() {
    switch (CompanyConfig.insightsTemplate) {
      case 'prodred':
        return 'InsightsProdRedScreen';
      case 'standard':
      default:
        return 'InsightsStandardScreen';
    }
  }

  /// Returns the template type being used for the current company
  static String getTemplateType() {
    switch (CompanyConfig.insightsTemplate) {
      case 'prodred':
        return 'ProdRed Insights (Statistics Only)';
      case 'standard':
      default:
        return 'Standard Insights (Full Toggle Functionality)';
    }
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
