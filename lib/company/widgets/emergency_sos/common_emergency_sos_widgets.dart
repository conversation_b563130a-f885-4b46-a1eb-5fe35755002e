import 'package:flutter/material.dart';
import 'package:nds_app/branding/company_config.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/utils/extension.dart';

/// Common Emergency SOS widgets and utilities
/// Contains reusable functions and widgets for Emergency SOS screens
class CommonEmergencySosWidgets {
  /// Gets the appropriate color for Emergency SOS based on company and user type
  /// B2C users always get colorGrey800, others get vehicle theme colors when available
  static Color getEmergencySosColor() {
    String hexColorInStr =
        sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";

    // B2C users always get colorGrey800, others get vehicle theme colors
    Color color =
        hexColorInStr.isNotEmpty && CompanyConfig.useVehicleThemeColors
            ? hexColorInStr.toColor()
            : colorGrey800;

    return color;
  }

  /// Helper method to replace @value placeholder in strings
  static String replaceValuePlaceholder(String text, String value) {
    return text.replaceAll("@value", value);
  }

  /// Creates a reusable error widget container
  static Widget buildErrorWidget(
      {required bool isVisible, required Widget widget}) {
    return Visibility(
      visible: isVisible,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: widget,
      ),
    );
  }
}
