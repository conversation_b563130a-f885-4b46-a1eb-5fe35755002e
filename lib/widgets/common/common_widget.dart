import 'dart:async';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_reactive_ble/flutter_reactive_ble.dart';
import 'package:intl/intl.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/common/image_urls.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/models/rider_test_details.dart';
import 'package:nds_app/repository/rider_test_repository.dart';
import 'package:nds_app/services/bluetooth_service.dart';
import 'package:nds_app/utils/extension.dart';
import 'package:nds_app/utils/time_filter_utils.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:nds_app/repository/leadership_status_repository.dart';
import 'package:nds_app/streams/leadership_status_stream.dart';
import 'package:nds_app/streams/leadership_combined_stream.dart';
import 'package:nds_app/repository/leadership_metadata_repository.dart';
import 'package:nds_app/models/leadership_metadata.dart';
import 'package:nds_app/models/leadership_status.dart';
import '../../common/colors.dart';
import '../../main.dart';

Widget getFuelSavingAndTotalDistanceRow(
    Dimensions dimensions,
    bool isConnected,
    int savedAmount,
    int totalDistance,
    String distanceUnit,
    BuildContext context) {
  ThemeMode themeMode = MyApp.of(context).getCurrentThemeMode();

  String formattedSavedAmount =
      NumberFormat.decimalPattern('en_IN').format(savedAmount);

  return Padding(
    padding: EdgeInsets.symmetric(horizontal: 6 / 414 * dimensions.width),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
            visible: isConnected == true,
            child: Row(
              children: [
                Text(
                  homeScreenText["text5"]!,
                  style: Theme.of(context).textTheme.labelMedium,
                ),
                SizedBox(
                  width: 8 / 896 * dimensions.width,
                ),
                SizedBox(
                    width: 10 / 414 * dimensions.width,
                    height: 10 / 896 * dimensions.height,
                    child: Image.asset(
                      alignment: Alignment.center,
                      homeScreenImages["rupee_icon"]!,
                      color: themeMode == ThemeMode.dark
                          ? colorGrey200
                          : colorGrey600,
                    )),
                Text(
                  formattedSavedAmount,
                  style: Theme.of(context).textTheme.labelSmall,
                ),
                SizedBox(
                  width: 4 / 414 * dimensions.width,
                ),
              ],
            )),
        Visibility(
          visible: isConnected,
          child: Tooltip(
            decoration: BoxDecoration(
                color: Theme.of(context).dividerColor.withOpacity(0.8),
                borderRadius:
                    BorderRadius.circular(4 / 414 * dimensions.width)),
            preferBelow: false,
            margin: EdgeInsets.only(left: 150 / 414 * dimensions.width),
            triggerMode: TooltipTriggerMode.tap,
            verticalOffset: 8 / 896 * dimensions.height,
            message: clusterScreenText["range"]!,
            textStyle: Theme.of(context)
                .textTheme
                .labelMedium
                ?.copyWith(color: colorWhite),
            child: Icon(
              Icons.info_outline,
              color: Theme.of(context).textTheme.labelSmall?.color,
              size: 12 / 414 * dimensions.width,
            ),
          ),
        ),
        Visibility(
          visible: isConnected,
          child: SizedBox(
            width: 41 / 414 * dimensions.width,
          ),
        ),
        Flexible(
          child: Text(
            homeScreenText["text1"]!,
            style: Theme.of(context).textTheme.labelMedium,
          ),
        ),
        SizedBox(
          width: 4 / 414 * dimensions.width,
        ),
        SizedBox(
          width: 14 / 414 * dimensions.width,
          height: 14 / 896 * dimensions.height,
          child: Image.asset(homeScreenImages['distance_icon']!),
        ),
        SizedBox(
          width: 8 / 414 * dimensions.width,
        ),
        SizedBox(
          width: 14 / 896 * dimensions.width,
        ),
        Flexible(
          child: Text(
            "$totalDistance $distanceUnit",
            style: Theme.of(context).textTheme.labelSmall,
          ),
        ),
      ],
    ),
  );
}

getFuelSavingDetailsContainer(
    Function action,
    Color color,
    Dimensions dimensions,
    int saved,
    int equivalent,
    int spent,
    BuildContext context) {
  String formattedSavedAmount =
      NumberFormat.decimalPattern('en_IN').format(saved);
  String formattedEquivalent =
      NumberFormat.decimalPattern('en_IN').format(equivalent);
  String formattedSpent = NumberFormat.decimalPattern('en_IN').format(spent);

  return Container(
    padding: EdgeInsets.symmetric(
      horizontal: 12 / 414 * dimensions.width,
    ),
    height: 180 / 896 * dimensions.height,
    //width: 374 / 414 * dimensions.width,
    margin: EdgeInsets.only(
        bottom: 16,
        left: 6 / 414 * dimensions.width,
        right: 6 / 414 * dimensions.width),
    decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(width: 1, color: colorGrey200),
        boxShadow: [
          BoxShadow(
              color: colorBlack.withOpacity(0.25),
              offset: const Offset(1, 3),
              blurRadius: 3,
              spreadRadius: 1),
          BoxShadow(
              color: colorWhite.withOpacity(0.25),
              offset: const Offset(-1, -3),
              blurRadius: 3,
              spreadRadius: 1)
        ]),
    child: Row(
      children: [
        FittedBox(
          child: SizedBox(
            width: 174 / 414 * dimensions.width,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 16 / 896 * dimensions.height,
                ),
                Row(
                  children: [
                    Text(
                      homeScreenText["text5"]!,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    SizedBox(
                      width: 5 / 414 * dimensions.width,
                    ),
                    SizedBox(
                        height: 20 / 896 * dimensions.height,
                        width: 20 / 414 * dimensions.width,
                        child: Image.asset(homeScreenImages["fuel_tree_icon"]!))
                  ],
                ),
                SizedBox(
                  height: 4 / 896 * dimensions.height,
                ),
                Text(
                  homeScreenText["text6"]!,
                  style: Theme.of(context).textTheme.labelSmall,
                ),
                SizedBox(
                  height: 40 / 896 * dimensions.height,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          SizedBox(
                              height: 20 / 896 * dimensions.height,
                              child: Image.asset(
                                homeScreenImages["rupee_icon"]!,
                                color: Theme.of(context)
                                    .primaryTextTheme
                                    .headlineLarge
                                    ?.color,
                              )),
                          Expanded(
                            child: AutoSizeText(formattedSavedAmount,
                                maxLines: 1,
                                style: Theme.of(context)
                                    .primaryTextTheme
                                    .headlineLarge),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      width: 4,
                    ),
                    Row(
                      children: [
                        SizedBox(
                            width: 12 / 414 * dimensions.width,
                            height: 12 / 896 * dimensions.height,
                            child: Image.asset(
                              homeScreenImages["rupee_icon"]!,
                              color:
                                  Theme.of(context).textTheme.labelSmall?.color,
                            )),
                        Text(
                          formattedSpent,
                          style: Theme.of(context).textTheme.labelSmall,
                        ),
                      ],
                    )
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      homeScreenText["text8"]!,
                      style: Theme.of(context).textTheme.labelSmall,
                    ),
                    Text(
                      homeScreenText["text9"]!,
                      style: Theme.of(context).textTheme.labelSmall,
                    )
                  ],
                ),
              ],
            ),
          ),
        ),
        SizedBox(
          width: 8 / 414 * dimensions.width,
        ),
        Expanded(
          child: SizedBox(
            width: 149 / 414 * dimensions.width,
            child: Column(children: [
              SizedBox(
                height: 16 / 896 * dimensions.height,
              ),
              Align(
                alignment: Alignment.topRight,
                child: Text(
                  homeScreenText["text10"]!,
                  style: Theme.of(context).textTheme.labelSmall,
                ),
              ),
              SizedBox(
                height: 5 / 896 * dimensions.height,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                      height: 130 / 896 * dimensions.height,
                      width: 40 / 414 * dimensions.width,
                      child: getFuelSavingTiles(
                          dimensions, spent, equivalent, true)),
                  Flexible(
                    child: SizedBox(
                      width: 8 / 414 * dimensions.width,
                    ),
                  ),
                  SizedBox(
                      height: 130 / 896 * dimensions.height,
                      width: 40 / 414 * dimensions.width,
                      child: getFuelSavingTiles(
                          dimensions, spent, equivalent, false)),
                  Flexible(
                    child: SizedBox(
                      width: 12 / 414 * dimensions.width,
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          SizedBox(
                              width: 12 / 414 * dimensions.width,
                              height: 12 / 896 * dimensions.height,
                              child: Image.asset(
                                homeScreenImages["rupee_icon"]!,
                                color: Theme.of(context)
                                    .textTheme
                                    .labelSmall
                                    ?.color,
                              )),
                          Text(
                            formattedEquivalent,
                            style: Theme.of(context).textTheme.labelSmall,
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 80 / 896 * dimensions.height,
                      ),
                      SizedBox(
                        height: 24 / 896 * dimensions.height,
                        width: 24 / 414 * dimensions.width,
                        child: Image.asset(
                          homeScreenImages["fuel_icon"]!,
                          color: Theme.of(context).textTheme.labelSmall?.color,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ]),
          ),
        ),
      ],
    ),
  );
}

getFuelSavingTiles(Dimensions dimensions, int totalSpent, int fuelEquivalent,
    bool isItSpentTitle) {
  int relativeTotalSaved = 0;
  if (isItSpentTitle == true && fuelEquivalent != 0) {
    relativeTotalSaved = 14 - ((totalSpent / fuelEquivalent) * 14).round();
  }

  return ListView.builder(
    padding: EdgeInsets.zero,
    physics: const NeverScrollableScrollPhysics(),
    itemCount: 14,
    itemBuilder: (context, index) {
      return Column(
        children: [
          Container(
            height: 6 / 896 * dimensions.height,
            width: 40 / 414 * dimensions.width,
            decoration: BoxDecoration(
              color: isItSpentTitle == true
                  ? (index < relativeTotalSaved
                      ? colorGrey300
                      : colorBlueFuelTile)
                  : colorGrey300,
              borderRadius: BorderRadius.all(
                  Radius.circular(40 / 414 * dimensions.width)),
            ),
          ),
          SizedBox(height: 3 / 896 * dimensions.height)
        ],
      );
    },
  );
}

scanForDevice() async {
  isGPSModeEnabled = false;
  BluetoothService ble = BluetoothService();
  // ble.resetService();
  await ble.checkPermission();

  bool isBluetoothReady = ble.checkIsBluetoothReady();
  if (!isBluetoothReady) {
    await Future.delayed(const Duration(seconds: 1));
    isBluetoothReady = ble.checkIsBluetoothReady();
    if (!isBluetoothReady) {
      // CustomToast.error(scanDevicesScreen['error1'] ?? "");
      return;
    }
  }
  if (await ble.checkPermission()) {
    // ignore: use_build_context_synchronously
    StreamSubscription<DiscoveredDevice> subscription =
        ble.scanDevices().listen((event) {
      debugPrint(
          "-----device ${event.name.toString()}--------- ${event.id}--------${event.manufacturerData}");
      if (event.name.toString().startsWith("pico0s")) {
        debugPrint("------HURRAY found it");
        isGPSModeEnabled = true;
      }
    });
    await Future.delayed(const Duration(seconds: 3));
    subscription.cancel();
  } else {
    // CustomToast.error(scanDevicesScreen['error2'] ?? "");
    return;
  }
}

class MyActivitiesContainer extends StatefulWidget {
  final Dimensions dimensions;

  const MyActivitiesContainer({
    Key? key,
    required this.dimensions,
  }) : super(key: key);

  @override
  State<MyActivitiesContainer> createState() => _MyActivitiesContainerState();
}

class _MyActivitiesContainerState extends State<MyActivitiesContainer> {
  String selectedTimeFilter = homeScreenText["text32"]!; // Default to "Today"
  Future<VehicleTestDetails>? _futureTestDetails;
  String? _connectedVehicleImei;
  OverlayEntry? _loadingOverlay; // For screen-level loading overlay
  final LeadershipStatusStream _leadershipStatusStream = LeadershipStatusStream();
  final LeadershipCombinedStream _leadershipCombinedStream = LeadershipCombinedStream();
  
  // Cache to avoid redundant API calls
  final Map<String, VehicleTestDetails> _vehicleDataCache = {};
  final Map<String, LeadershipMetadata> _metadataCache = {};
  final Map<String, LeadershipStatusResponse> _statusCache = {};
  String? _lastCacheKey;
  static const int _maxCacheSize = 10; // Limit cache size to prevent memory issues

  @override
  void initState() {
    super.initState();
    _futureTestDetails = _createTodaysFuture();
    _loadInitialData();
  }

  @override
  void dispose() {
    // Remove loading overlay if it exists
    _hideLoadingOverlay();
    // Clear cache to prevent memory leaks
    _vehicleDataCache.clear();
    _metadataCache.clear();
    _statusCache.clear();
    super.dispose();
  }

  /// Shows a screen-level loading overlay
  void _showLoadingOverlay() {
    if (_loadingOverlay != null) return; // Already showing

    _loadingOverlay = OverlayEntry(
      builder: (context) => Material(
        color: Colors.black.withOpacity(0.7),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                isTwoWheels
                    ? loaderGifImages['2Wheels']!
                    : loaderGifImages['3Wheels']!,
              ),
              SizedBox(height: 16),
              Text(
                'Loading...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_loadingOverlay!);
  }

  /// Hides the screen-level loading overlay
  void _hideLoadingOverlay() {
    _loadingOverlay?.remove();
    _loadingOverlay = null;
  }

  Future<void> _loadInitialData() async {
    await _loadConnectedVehicleImei();
    if (mounted) {
      // Preload today's data immediately without showing loading indicator for initial load
      final timeRange = TimeFilterUtils.calculateTimeRange(selectedTimeFilter);
      final cacheKey = '${selectedTimeFilter}_${timeRange['startTime']}_${timeRange['endTime']}_${_connectedVehicleImei ?? 'no_imei'}';
      
      try {
        // Quick parallel load for initial data
        if (isProdRedLeadershipValue) {
          final futures = await Future.wait([
            VehicleTestRepository().getVehicleTestDetails(
              imei: _connectedVehicleImei,
              startTime: timeRange['startTime'],
              endTime: timeRange['endTime'],
            ).timeout(const Duration(seconds: 8)),
            LeadershipMetadataRepository().getMetadata(
              startTime: timeRange['startTime'] ?? 0,
              endTime: timeRange['endTime'] ?? 0,
            ).timeout(const Duration(seconds: 8)),
            LeadershipStatusRepository().getStatus(
              startTime: timeRange['startTime'] ?? 0,
              endTime: timeRange['endTime'] ?? 0,
            ).timeout(const Duration(seconds: 8)),
          ]);

          final testDetails = futures[0] as VehicleTestDetails;
          final metadata = futures[1] as LeadershipMetadata?;
          final status = futures[2] as LeadershipStatusResponse?;

          // Cache immediately with size limit
          _addToCache(cacheKey, testDetails, metadata, status);

          if (mounted) {
            setState(() {
              _futureTestDetails = Future.value(testDetails);
            });
            _leadershipStatusStream.update(status);
            _leadershipCombinedStream.updateBoth(metadata, status);
          }
        } else {
          final testDetails = await VehicleTestRepository().getVehicleTestDetails(
            imei: _connectedVehicleImei,
            startTime: timeRange['startTime'],
            endTime: timeRange['endTime'],
          ).timeout(const Duration(seconds: 8));

          _addToCache(cacheKey, testDetails, null, null);

          if (mounted) {
            setState(() {
              _futureTestDetails = Future.value(testDetails);
            });
          }
        }
      } catch (e) {
        debugPrint('Initial load error: $e');
        // Fall back to regular refresh if initial load fails
        _refreshData();
      }
    }
  }

  Future<void> _loadConnectedVehicleImei() async {
    final prefs = await SharedPreferences.getInstance();
    _connectedVehicleImei = prefs.getString(connectedVehicleImeiNo);
  }

  /// Helper method to add items to cache with size management
  void _addToCache(String cacheKey, VehicleTestDetails testDetails, 
                   LeadershipMetadata? metadata, LeadershipStatusResponse? status) {
    // Clear oldest entries if cache is full
    if (_vehicleDataCache.length >= _maxCacheSize) {
      final oldestKey = _vehicleDataCache.keys.first;
      _vehicleDataCache.remove(oldestKey);
      _metadataCache.remove(oldestKey);
      _statusCache.remove(oldestKey);
    }
    
    // Add new cache entries
    _vehicleDataCache[cacheKey] = testDetails;
    if (metadata != null) _metadataCache[cacheKey] = metadata;
    if (status != null) _statusCache[cacheKey] = status;
    _lastCacheKey = cacheKey;
  }

  /// Force load today's data immediately
  void _loadTodaysData() {
    setState(() {
      selectedTimeFilter = homeScreenText["text32"]!; // Ensure it's "Today"
    });
    _refreshData();
  }

  /// Create a Future for today's data as fallback
  Future<VehicleTestDetails> _createTodaysFuture() {
    final todayString = homeScreenText["text32"]!;
    final todayTimeRange = TimeFilterUtils.calculateTimeRange(todayString);
    return VehicleTestRepository().getVehicleTestDetails(
      imei: _connectedVehicleImei,
      startTime: todayTimeRange['startTime'],
      endTime: todayTimeRange['endTime'],
    );
  }

  void _refreshData() async {
    if (!mounted) {
      return;
    }

    final timeRange = TimeFilterUtils.calculateTimeRange(selectedTimeFilter);
    final cacheKey = '${selectedTimeFilter}_${timeRange['startTime']}_${timeRange['endTime']}_${_connectedVehicleImei ?? 'no_imei'}';
    
    // Check if we have cached data for this exact request
    if (_vehicleDataCache.containsKey(cacheKey) && _lastCacheKey == cacheKey) {
      // Use cached data immediately - no loading needed
      final cachedTestDetails = _vehicleDataCache[cacheKey]!;
      
      if (mounted) {
        setState(() {
          _futureTestDetails = Future.value(cachedTestDetails);
        });
        
        // If leadership, also update streams with cached data
        if (isProdRedLeadershipValue) {
          final metadata = _metadataCache[cacheKey];
          final status = _statusCache[cacheKey];
          _leadershipStatusStream.update(status);
          _leadershipCombinedStream.updateBoth(metadata, status);
        }
      }
      return; // Exit early with cached data
    }

    // Show screen-level loading overlay for new data
    _showLoadingOverlay();

    try {
      if (isProdRedLeadershipValue) {
        // For leadership users, call all APIs in parallel with timeout for faster response
        final futures = await Future.wait([
          VehicleTestRepository().getVehicleTestDetails(
            imei: _connectedVehicleImei,
            startTime: timeRange['startTime'],
            endTime: timeRange['endTime'],
          ).timeout(const Duration(seconds: 10)),
          LeadershipMetadataRepository().getMetadata(
            startTime: timeRange['startTime'] ?? 0,
            endTime: timeRange['endTime'] ?? 0,
          ).timeout(const Duration(seconds: 10)),
          LeadershipStatusRepository().getStatus(
            startTime: timeRange['startTime'] ?? 0,
            endTime: timeRange['endTime'] ?? 0,
          ).timeout(const Duration(seconds: 10)),
        ]);

        final testDetails = futures[0] as VehicleTestDetails;
        final metadata = futures[1] as LeadershipMetadata?;
        final status = futures[2] as LeadershipStatusResponse?;

        // Cache the results immediately with size limit
        _addToCache(cacheKey, testDetails, metadata, status);

        if (mounted) {
          setState(() {
            _futureTestDetails = Future.value(testDetails);
          });
          
          // Hide loading overlay
          _hideLoadingOverlay();
          
          // Update leadership streams
          _leadershipStatusStream.update(status);
          _leadershipCombinedStream.updateBoth(metadata, status);
        }
      } else {
        // For regular users, just call the main API with timeout
        final testDetails = await VehicleTestRepository().getVehicleTestDetails(
          imei: _connectedVehicleImei,
          startTime: timeRange['startTime'],
          endTime: timeRange['endTime'],
        ).timeout(const Duration(seconds: 10));

        // Cache the result with size limit
        _addToCache(cacheKey, testDetails, null, null);

        if (mounted) {
          setState(() {
            _futureTestDetails = Future.value(testDetails);
          });
          
          // Hide loading overlay
          _hideLoadingOverlay();
        }
      }
    } catch (e) {
      debugPrint('Error refreshing data: $e');
      if (mounted) {
        // Hide loading overlay even on error
        _hideLoadingOverlay();
      }
    }
  }


  @override
  Widget build(BuildContext context) {
    if (_futureTestDetails == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _loadTodaysData();
        }
      });
    }
    
    return FutureBuilder<VehicleTestDetails>(
      future: _futureTestDetails ?? _createTodaysFuture(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          debugPrint('Snapshot error: ${snapshot.error}');
        }
        
        VehicleTestDetails testDetails =
            snapshot.hasData ? snapshot.data! : VehicleTestDetails.empty();

        return Container(
          padding: EdgeInsets.symmetric(
            horizontal: 8 / 414 * widget.dimensions.width,
            vertical: 16 / 896 * widget.dimensions.height,
          ),
          margin: EdgeInsets.only(
            bottom: 16,
            left: 6 / 414 * widget.dimensions.width,
            right: 6 / 414 * widget.dimensions.width,
          ),
          decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(width: 1, color: colorGrey200),
              boxShadow: [
                BoxShadow(
                    color: colorBlack.withOpacity(0.25),
                    offset: const Offset(1, 3),
                    blurRadius: 3,
                    spreadRadius: 1),
                BoxShadow(
                    color: colorWhite.withOpacity(0.25),
                    offset: const Offset(-1, -3),
                    blurRadius: 3,
                    spreadRadius: 1)
              ]),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(
                  left: 8 / 414 * widget.dimensions.width,
                  bottom: 16 / 896 * widget.dimensions.height,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      isProdRedLeadership 
                          ? homeScreenText["overview_title"]! 
                          : homeScreenText["my_activities_title"]!,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    Container(
                      width: 160 / 414 * widget.dimensions.width,
                      padding: EdgeInsets.symmetric(
                        horizontal: 12 / 414 * widget.dimensions.width,
                        vertical: 12 / 896 * widget.dimensions.height,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).scaffoldBackgroundColor,
                        borderRadius: BorderRadius.circular(8.0),
                        border: Border.all(width: 1, color: colorGrey300),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: selectedTimeFilter,
                          isDense: true,
                          isExpanded: true,
                          icon: Icon(
                            Icons.keyboard_arrow_down,
                            size: 20,
                            color: Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black87,
                          ),
                          dropdownColor: Theme.of(context).scaffoldBackgroundColor,
                          borderRadius: BorderRadius.circular(8.0),
                          itemHeight: 48,
                          items: [
                            homeScreenText["text32"]!,
                            homeScreenText["text33"]!,
                            homeScreenText["text34"]!,
                            homeScreenText["text36"]!,
                            homeScreenText["text35"]!,
                            homeScreenText["text37"]!,
                          ].map<DropdownMenuItem<String>>((String value) {
                            bool isSelected = value == selectedTimeFilter;
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 2,
                                ),
                                child: AutoSizeText(
                                  maxLines: 1,
                                  value,
                                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 13,
                                    color: isSelected
                                        ? (Theme.of(context).brightness == Brightness.dark
                                            ? Colors.white
                                            : Colors.black)
                                        : (Theme.of(context).brightness == Brightness.dark
                                            ? Colors.grey.shade400
                                            : Colors.grey.shade600),
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            if (newValue != null && mounted && _loadingOverlay == null) {
                              setState(() {
                                selectedTimeFilter = newValue;
                              });
                              _refreshData(); // Refresh data when filter changes
                            } else {
                              debugPrint('Dropdown change ignored - newValue: $newValue, mounted: $mounted, loading: ${_loadingOverlay != null}');
                            }
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 165 / 896 * widget.dimensions.height,
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 4 / 414 * widget.dimensions.width,
                          vertical: 20 / 896 * widget.dimensions.height,
                        ),
                        margin: EdgeInsets.only(
                          bottom: 16,
                          left: 6 / 414 * widget.dimensions.width,
                          right: 6 / 414 * widget.dimensions.width,
                        ),
                        decoration: BoxDecoration(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            borderRadius: BorderRadius.circular(8.0),
                            border: Border.all(width: 1, color: colorGrey200),
                            boxShadow: [
                              BoxShadow(
                                  color: colorBlack.withOpacity(0.25),
                                  offset: const Offset(1, 3),
                                  blurRadius: 3,
                                  spreadRadius: 1),
                              BoxShadow(
                                  color: colorWhite.withOpacity(0.25),
                                  offset: const Offset(-1, -3),
                                  blurRadius: 3,
                                  spreadRadius: 1)
                            ]),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              isProdRedLeadership 
                                  ? homeScreenText["running_status"]! 
                                  : homeScreenText["ride_hours_label"]!,
                              style: Theme.of(context).textTheme.labelMedium?.copyWith(fontSize: 14),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 16 / 896 * widget.dimensions.height),
                            isProdRedLeadership
                                ? StreamBuilder<LeadershipCombinedData>(
                                    stream: _leadershipCombinedStream.stream,
                                    builder: (context, snapshot) {
                                      final metadata = snapshot.data?.metadata;
                                      return Text(
                                        '${metadata?.totalRunning ?? 0} / ${metadata?.totalVehicles ?? 0}',
                                        style: Theme.of(context).primaryTextTheme.bodyLarge,
                                      );
                                    },
                                  )
                                : Text(
                                    TimeFilterUtils.formatRideTime(testDetails.rideTime),
                                    style: Theme.of(context).primaryTextTheme.bodyLarge,
                                  ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 4 / 414 * widget.dimensions.width),
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 4 / 414 * widget.dimensions.width,
                          vertical: 20 / 896 * widget.dimensions.height,
                        ),
                        margin: EdgeInsets.only(
                          bottom: 16,
                          left: 6 / 414 * widget.dimensions.width,
                          right: 6 / 414 * widget.dimensions.width,
                        ),
                        decoration: BoxDecoration(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            borderRadius: BorderRadius.circular(8.0),
                            border: Border.all(width: 1, color: colorGrey200),
                            boxShadow: [
                              BoxShadow(
                                  color: colorBlack.withOpacity(0.25),
                                  offset: const Offset(1, 3),
                                  blurRadius: 3,
                                  spreadRadius: 1),
                              BoxShadow(
                                  color: colorWhite.withOpacity(0.25),
                                  offset: const Offset(-1, -3),
                                  blurRadius: 3,
                                  spreadRadius: 1)
                            ]),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              isProdRedLeadership 
                                  ? homeScreenText["trips_label"]! 
                                  : homeScreenText["ride_distance_label"]!,
                              style: Theme.of(context).textTheme.labelMedium?.copyWith(fontSize: 14),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: 16 / 896 * widget.dimensions.height),
                            isProdRedLeadership
                                ? StreamBuilder<LeadershipCombinedData>(
                                    stream: _leadershipCombinedStream.stream,
                                    builder: (context, snapshot) {
                                      final metadata = snapshot.data?.metadata;
                                      return Text(
                                        '${metadata?.totalTripCount ?? 0}',
                                        style: Theme.of(context).primaryTextTheme.bodyLarge,
                                      );
                                    },
                                  )
                                : Text(
                                    TimeFilterUtils.formatRideDistance(testDetails.rideDistance),
                                    style: Theme.of(context).primaryTextTheme.bodyLarge,
                                  ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 8 / 896 * widget.dimensions.height),
              _buildSecondRowContainers(context, isProdRedLeadership, testDetails),
              Visibility(
                visible: !isProdRedLeadership,
                child: Padding(
                  padding: EdgeInsets.only(
                    left: 8 / 414 * widget.dimensions.width,
                    top: 16 / 896 * widget.dimensions.height,
                  ),
                  child: Row(
                    children: [
                      Text(
                        homeScreenText["text27"]!,
                        style: Theme.of(context).textTheme.labelMedium,
                      ),
                      SizedBox(width: 8 / 414 * widget.dimensions.width),
                      SizedBox(
                          width: 16 / 414 * widget.dimensions.width,
                          child: Image.asset(homeScreenImages['distance_icon']!)),
                      SizedBox(width: 8 / 414 * widget.dimensions.width),
                      Text(
                        "${testDetails.testRecords} ${homeScreenText["text28"]!}",
                        style: Theme.of(context).textTheme.labelSmall,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      });
    }


    /// Builds the second row containers based on user type (leadership vs raider)
    Widget _buildSecondRowContainers(BuildContext context, bool isLeadership, VehicleTestDetails testDetails) {
      return SizedBox(
        height: 165 / 896 * widget.dimensions.height,
        child: Row(
          children: [
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 4 / 414 * widget.dimensions.width,
                  vertical: 20 / 896 * widget.dimensions.height,
                ),
                margin: EdgeInsets.only(
                  bottom: 16,
                  left: 6 / 414 * widget.dimensions.width,
                  right: 6 / 414 * widget.dimensions.width,
                ),
                decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(width: 1, color: colorGrey200),
                    boxShadow: [
                      BoxShadow(
                          color: colorBlack.withOpacity(0.25),
                          offset: const Offset(1, 3),
                          blurRadius: 3,
                          spreadRadius: 1),
                      BoxShadow(
                          color: colorWhite.withOpacity(0.25),
                          offset: const Offset(-1, -3),
                          blurRadius: 3,
                          spreadRadius: 1)
                    ]),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      isLeadership ? homeScreenText["alerts_label"]! : homeScreenText["trips_label_rider"]!,
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 16 / 896 * widget.dimensions.height),
                    isLeadership
                        ? StreamBuilder<LeadershipCombinedData>(
                            stream: _leadershipCombinedStream.stream,
                            builder: (context, snapshot) {
                              final metadata = snapshot.data?.metadata;
                              return Text(
                                '${metadata?.totalAlertCount ?? 0} / ${metadata?.totalVehicles ?? 0}',
                                style: Theme.of(context).primaryTextTheme.bodyLarge,
                              );
                            },
                          )
                        : Text(
                            TimeFilterUtils.formatCount(testDetails.totalTripCount),
                            style: Theme.of(context).primaryTextTheme.bodyLarge,
                          ),
                  ],
                ),
              ),
            ),
            SizedBox(width: 4 / 414 * widget.dimensions.width),
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 4 / 414 * widget.dimensions.width,
                  vertical: 20 / 896 * widget.dimensions.height,
                ),
                margin: EdgeInsets.only(
                  bottom: 16,
                  left: 6 / 414 * widget.dimensions.width,
                  right: 6 / 414 * widget.dimensions.width,
                ),
                decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: BorderRadius.circular(8.0),
                    border: Border.all(width: 1, color: colorGrey200),
                    boxShadow: [
                      BoxShadow(
                          color: colorBlack.withOpacity(0.25),
                          offset: const Offset(1, 3),
                          blurRadius: 3,
                          spreadRadius: 1),
                      BoxShadow(
                          color: colorWhite.withOpacity(0.25),
                          offset: const Offset(-1, -3),
                          blurRadius: 3,
                          spreadRadius: 1)
                    ]),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      isLeadership ? homeScreenText["alarms_label"]! : homeScreenText["test_rides_label"]!,
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 16 / 896 * widget.dimensions.height),
                    isLeadership
                        ? StreamBuilder<LeadershipCombinedData>(
                            stream: _leadershipCombinedStream.stream,
                            builder: (context, snapshot) {
                              final metadata = snapshot.data?.metadata;
                              return Text(
                                '${metadata?.totalAlarmCount ?? 0} / ${metadata?.totalVehicles ?? 0}',
                                style: Theme.of(context).primaryTextTheme.bodyLarge,
                              );
                            },
                          )
                        : Text(
                            TimeFilterUtils.formatCount(testDetails.testRideCount),
                            style: Theme.of(context).primaryTextTheme.bodyLarge,
                          ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

List<String> getTimeInHourAndMinFromMins(double? min) {
  String hours = '0';
  String mins = '0';
  if (min != null) {
    double hour = min / 60;
    int truncHour = hour.truncate();
    hours = truncHour.toString();
    mins = ((hour - truncHour) * 60).truncate().toString();
  }
  return [hours, mins];
}

Widget getMyActivitiesContainer(BuildContext context, Dimensions dimensions) {
  return MyActivitiesContainer(dimensions: dimensions);
}

Widget getAutoSOSContainer(BuildContext context, Dimensions dimensions,
    VoidCallback onSOSTap, Color iconColor, ColorType colorType) {
  // Apply the same color logic as bottom navigation
  Color vehicleColor = currentVehicleStatus == VehicleStatus.connected
      ? colorType == ColorType.light
          ? Theme.of(context).highlightColor
          : iconColor
      : loginThemeColor;

  return Container(
    padding: EdgeInsets.symmetric(
      horizontal: 8 / 414 * dimensions.width,
      vertical: 16 / 896 * dimensions.height,
    ),
    margin: EdgeInsets.only(
      bottom: 16,
      left: 6 / 414 * dimensions.width,
      right: 6 / 414 * dimensions.width,
    ),
    decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(width: 1, color: colorGrey200),
        boxShadow: [
          BoxShadow(
              color: colorBlack.withOpacity(0.25),
              offset: const Offset(1, 3),
              blurRadius: 3,
              spreadRadius: 1),
          BoxShadow(
              color: colorWhite.withOpacity(0.25),
              offset: const Offset(-1, -3),
              blurRadius: 3,
              spreadRadius: 1)
        ]),
    child: Padding(
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 0),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  homeScreenText["auto_sos_title"]!,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                SizedBox(height: 8 / 896 * dimensions.height),
                Text(
                  homeScreenText["auto_sos_description"]!,
                  style: Theme.of(context).textTheme.labelSmall,
                ),
                SizedBox(height: 8 / 896 * dimensions.height),
                InkWell(
                  onTap: onSOSTap,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 30 / 414 * dimensions.width,
                      vertical: 8 / 896 * dimensions.height,
                    ),
                    decoration: BoxDecoration(
                      color: vehicleColor,
                      borderRadius: BorderRadius.circular(20.0),
                    ),
                    child: Text(
                      homeScreenText["auto_sos_button"]!,
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: colorWhite,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: 16 / 414 * dimensions.width),
          Image.asset(
            alignment: Alignment.center,
            homeScreenImages["bell_icon"]!,
            color: vehicleColor,
          ),
        ],
      ),
    ),
  );
}

Color getColor() {
  String hexColorInStr =
      sharedPreferences!.getString(vehicleThemeColorInHex) ?? "";
  Color color = hexColorInStr.isNotEmpty && !isB2CUser
      ? hexColorInStr.toColor()
      : colorGrey800;

  return color;
}
